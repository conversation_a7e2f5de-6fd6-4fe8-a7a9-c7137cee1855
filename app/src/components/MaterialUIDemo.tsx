import React, { useState } from 'react';
import {
  Box,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  TextField,
  Grid,
  Paper,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  LinearProgress,
  Switch,
  FormControlLabel,
  Tab,
  Tabs,
  AppBar,
  Toolbar,
  IconButton,
  Badge,
  Fab,
  Snackbar,
} from '@mui/material';
import {
  Home,
  Settings,
  Notifications,
  Person,
  Email,
  Phone,
  Work,
  Add,
  Edit,
  Delete,
  Save,
  Cancel,
  Search,
  Menu,
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export const MaterialUIDemo: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [textValue, setTextValue] = useState('');
  const [switchChecked, setSwitchChecked] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [progress, setProgress] = useState(30);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const demoUsers = [
    { name: 'John Doe', email: '<EMAIL>', role: 'Developer' },
    { name: 'Jane Smith', email: '<EMAIL>', role: 'Designer' },
    { name: 'Bob Johnson', email: '<EMAIL>', role: 'Manager' },
  ];

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header with AppBar */}
      <AppBar position="static" sx={{ mb: 4, borderRadius: 1 }}>
        <Toolbar>
          <IconButton edge="start" color="inherit" aria-label="menu">
            <Menu />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Material UI Demo
          </Typography>
          <IconButton color="inherit">
            <Badge badgeContent={4} color="error">
              <Notifications />
            </Badge>
          </IconButton>
          <IconButton color="inherit">
            <Person />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* Tabs Navigation */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} centered>
          <Tab icon={<Home />} label="Components" />
          <Tab icon={<Settings />} label="Forms" />
          <Tab icon={<Person />} label="Data Display" />
        </Tabs>
      </Paper>

      {/* Tab Panel 1: Basic Components */}
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          {/* Buttons and Actions */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Buttons & Actions
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                  <Button variant="contained" startIcon={<Add />}>
                    Add Item
                  </Button>
                  <Button variant="outlined" startIcon={<Edit />}>
                    Edit
                  </Button>
                  <Button variant="text" startIcon={<Delete />} color="error">
                    Delete
                  </Button>
                </Box>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Button size="small">Small</Button>
                  <Button size="medium">Medium</Button>
                  <Button size="large">Large</Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Chips and Badges */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Chips & Status
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                  <Chip label="Active" color="success" />
                  <Chip label="Pending" color="warning" />
                  <Chip label="Inactive" color="error" />
                  <Chip label="Draft" variant="outlined" />
                </Box>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <Badge badgeContent={4} color="primary">
                    <Email />
                  </Badge>
                  <Badge badgeContent={99} color="secondary">
                    <Notifications />
                  </Badge>
                  <Badge variant="dot" color="error">
                    <Phone />
                  </Badge>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Progress and Alerts */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Progress & Alerts
                </Typography>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    Progress: {progress}%
                  </Typography>
                  <LinearProgress variant="determinate" value={progress} />
                </Box>
                <Alert severity="success" sx={{ mb: 1 }}>
                  This is a success alert!
                </Alert>
                <Alert severity="warning" sx={{ mb: 1 }}>
                  This is a warning alert!
                </Alert>
                <Alert severity="error">
                  This is an error alert!
                </Alert>
              </CardContent>
              <CardActions>
                <Button 
                  onClick={() => setProgress(Math.min(100, progress + 10))}
                  disabled={progress >= 100}
                >
                  Increase Progress
                </Button>
                <Button 
                  onClick={() => setProgress(Math.max(0, progress - 10))}
                  disabled={progress <= 0}
                >
                  Decrease Progress
                </Button>
              </CardActions>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Tab Panel 2: Forms */}
      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Form Controls
                </Typography>
                <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <TextField
                    label="Full Name"
                    variant="outlined"
                    fullWidth
                    value={textValue}
                    onChange={(e) => setTextValue(e.target.value)}
                  />
                  <TextField
                    label="Email"
                    type="email"
                    variant="outlined"
                    fullWidth
                  />
                  <TextField
                    label="Message"
                    multiline
                    rows={4}
                    variant="outlined"
                    fullWidth
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={switchChecked}
                        onChange={(e) => setSwitchChecked(e.target.checked)}
                      />
                    }
                    label="Enable notifications"
                  />
                </Box>
              </CardContent>
              <CardActions>
                <Button variant="contained" startIcon={<Save />}>
                  Save
                </Button>
                <Button variant="outlined" startIcon={<Cancel />}>
                  Cancel
                </Button>
              </CardActions>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Search
                </Typography>
                <TextField
                  label="Search..."
                  variant="outlined"
                  fullWidth
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'action.active' }} />,
                  }}
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Tab Panel 3: Data Display */}
      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  User List
                </Typography>
                <List>
                  {demoUsers.map((user, index) => (
                    <React.Fragment key={index}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar>
                            {user.name.split(' ').map(n => n[0]).join('')}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={user.name}
                          secondary={`${user.email} • ${user.role}`}
                        />
                      </ListItem>
                      {index < demoUsers.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Statistics
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      1,234
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Users
                    </Typography>
                  </Paper>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      567
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Sessions
                    </Typography>
                  </Paper>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setSnackbarOpen(true)}
      >
        <Add />
      </Fab>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        message="Action completed successfully!"
      />
    </Container>
  );
};
