import { useState, useEffect } from "react";
import { ThemeProvider, CssBaseline } from "@mui/material";
import reactLogo from "./assets/react.svg";
import viteLogo from "/vite.svg";
import "./App.css";
import type { AppConfig } from "./types";
import { AuthProvider } from "./auth/AuthProvider";
import { LoginButton } from "./components/LoginButton";
import { MaterialUIDemo } from "./components/MaterialUIDemo";
import { DataTableDemo } from "./components/DataTableDemo";
import { ThemeToggle } from "./components/ThemeToggle";
import { theme, darkTheme } from "./theme/theme";

interface DeploymentInfo {
  buildTime: string;
  environment: string;
}

function App() {
  const [count, setCount] = useState(0);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [showMaterialUIDemo, setShowMaterialUIDemo] = useState(false);
  const [deploymentInfo] = useState<DeploymentInfo>({
    buildTime: new Date().toISOString(),
    environment: import.meta.env.MODE || "development",
  });

  // Example of using the TypeScript types
  const [appConfig] = useState<AppConfig>({
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || "https://api.example.com",
    environment:
      (import.meta.env.MODE as "development" | "staging" | "production") ||
      "development",
    version: "1.0.0",
    features: {
      enableNotifications: true,
      enableReporting: false,
      enableAdvancedFiltering: true,
    },
  });

  useEffect(() => {
    // Simulate fetching deployment info
    console.log("Scheduling Uplift Frontend - PoC deployed successfully!");
  }, []);

  return (
    <ThemeProvider theme={isDarkMode ? darkTheme : theme}>
      <CssBaseline />
      <AuthProvider>
        <div style={{ position: 'relative' }}>
          {/* Theme Toggle Button */}
          <div style={{ position: 'absolute', top: 10, right: 10, zIndex: 1000 }}>
            <ThemeToggle
              isDarkMode={isDarkMode}
              onToggle={() => setIsDarkMode(!isDarkMode)}
            />
          </div>

          <div>
            <a href="https://vite.dev" target="_blank" rel="noopener noreferrer">
              <img src={viteLogo} className="logo" alt="Vite logo" />
            </a>
            <a href="https://react.dev" target="_blank" rel="noopener noreferrer">
              <img src={reactLogo} className="logo react" alt="React logo" />
            </a>
          </div>
          <h1>Scheduling Uplift Frontend - PoC</h1>

          {/* Azure AD Authentication */}
          <div style={{ margin: "2rem 0" }}>
            <LoginButton />
          </div>

          {/* Material UI Demo Toggle */}
          <div style={{ margin: "2rem 0" }}>
            <button
              onClick={() => setShowMaterialUIDemo(!showMaterialUIDemo)}
              style={{
                padding: "12px 24px",
                fontSize: "16px",
                backgroundColor: isDarkMode ? "#1976d2" : "#1976d2",
                color: "white",
                border: "none",
                borderRadius: "8px",
                cursor: "pointer",
                marginRight: "10px"
              }}
            >
              {showMaterialUIDemo ? "Hide Material UI Demo" : "Show Material UI Demo"}
            </button>
          </div>

          {showMaterialUIDemo && (
            <>
              <MaterialUIDemo />
              <div style={{ marginTop: "3rem" }}>
                <DataTableDemo />
              </div>
            </>
          )}

          <div className="card">
            <button onClick={() => setCount((count) => count + 1)}>
              count is {count}
            </button>
            <p>
              Edit <code>src/App.tsx</code> and save to test HMR
            </p>
            <div
              style={{
                marginTop: "20px",
                padding: "10px",
                background: "#f0f0f0",
                borderRadius: "5px",
              }}
            >
              <h3>Deployment Info</h3>
              <p>
                <strong>Environment:</strong> {deploymentInfo.environment}
              </p>
              <p>
                <strong>Build Time:</strong> {deploymentInfo.buildTime}
              </p>
              <p>
                <strong>Status:</strong> ✅ Successfully deployed to Azure Storage
                Static Website
              </p>
            </div>
            <div
              style={{
                marginTop: "20px",
                padding: "10px",
                background: "#e8f4f8",
                borderRadius: "5px",
              }}
            >
              <h3>TypeScript Configuration</h3>
              <p>
                <strong>API Base URL:</strong> {appConfig.apiBaseUrl}
              </p>
              <p>
                <strong>Version:</strong> {appConfig.version}
              </p>
              <p>
                <strong>Features:</strong>
              </p>
              <ul
                style={{
                  textAlign: "left",
                  margin: "0 auto",
                  display: "inline-block",
                }}
              >
                <li>
                  Notifications:{" "}
                  {appConfig.features.enableNotifications ? "✅" : "❌"}
                </li>
                <li>
                  Reporting: {appConfig.features.enableReporting ? "✅" : "❌"}
                </li>
                <li>
                  Advanced Filtering:{" "}
                  {appConfig.features.enableAdvancedFiltering ? "✅" : "❌"}
                </li>
              </ul>
            </div>
          </div>

          <p className="read-the-docs">
            This is a proof of concept for React + Vite + TypeScript deployment to
            Azure using Bicep and Azure DevOps with Azure AD authentication
          </p>
        </div>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
